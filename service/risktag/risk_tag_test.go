package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestRiskTagCalcScore(t *testing.T) {
	Convey("TestRiskTagCalcScore", t, func() {
		// 创建测试用的 RiskTag 实例
		riskTag := &RiskTag{
			&DeviceRiskTag{
				RiskTag: &model.RiskTag{
					Id:        400,
					ClientId:  1004,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestRiskTag",
					Code:      "test_risk_tag",
					Status:    1,
					TagUnit:   1, // bool
					RiskType:  1, // 增分
					ScoreType: 1, // 静态
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							DeviceRiskLabels: []string{
								"high_risk,fraud",
								"bot,automation",
								"suspicious",
							},
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      1800, // 30分钟
							ResultScore: 100,  // 100分
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1004,
			ModelCode: "test_model",
			Nid:       "test_user_004",
			Doid:      "test_device_004",
			Now:       time.Now(),
		}

		ctx := context.Background()

		// 清理测试数据
		scoreKey := riskTag.ScoreKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)

		var scoreItems = make([]*ScoreItem, 0)

		Convey("When device ID is empty", func() {
			// 设置空的设备ID
			paramWithoutDoid := &BaseParam{
				ClientId:  1004,
				ModelCode: "test_model",
				Nid:       "test_user_004",
				Doid:      "", // 空的设备ID
				Now:       time.Now(),
			}

			err := riskTag.CalcScore(ctx, paramWithoutDoid, &scoreItems)
			So(err, ShouldBeNil)

			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("When no risk labels are configured", func() {
			// 创建没有风险标签配置的实例
			riskTagWithoutLabels := &RiskTag{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       401,
						ClientId: 1004,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{}, // 空的风险标签
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      1800,
								ResultScore: 100,
							},
						},
					},
				},
			}

			err := riskTagWithoutLabels.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("When device has no risk labels", func() {
			// 这个测试需要模拟数据库返回空的风险标签
			// 由于我们无法直接控制数据库返回值，这里假设设备没有风险标签
			err := riskTag.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 如果设备没有风险标签，应该没有评分
			// 注意：这个测试的结果取决于数据库中的实际数据
		})

		Convey("When device has matching risk labels", func() {
			// 这个测试需要预先在数据库中设置风险标签数据
			// 由于测试环境的限制，我们主要测试逻辑流程

			// 模拟设备有风险标签的情况
			// 实际测试中需要先在数据库中插入测试数据
			err := riskTag.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 如果匹配到风险标签，应该有评分结果
			// 具体结果取决于数据库中的数据
		})
	})
}

func TestRiskTagEventRecord(t *testing.T) {
	Convey("TestRiskTagEventRecord", t, func() {
		riskTag := &RiskTag{
			&DeviceRiskTag{
				RiskTag: &model.RiskTag{
					Id:       402,
					ClientId: 1004,
					ModelId:  "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1004,
			ModelCode: "test_model",
			Nid:       "test_user_004",
			Doid:      "test_device_004",
			Now:       time.Now(),
		}

		ctx := context.Background()

		// EventRecord 对于 RiskTag 类型应该什么都不做
		err := riskTag.EventRecord(ctx, param)
		So(err, ShouldBeNil)
	})
}

func TestArrayContains(t *testing.T) {
	Convey("TestArrayContains", t, func() {
		Convey("When all elements exist", func() {
			arr := []string{"high_risk", "fraud", "bot", "automation"}
			str := []string{"high_risk", "fraud"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeTrue)
		})

		Convey("When some elements do not exist", func() {
			arr := []string{"high_risk", "fraud"}
			str := []string{"high_risk", "bot"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeFalse)
		})

		Convey("当所有元素都不存在时", func() {
			arr := []string{"normal", "safe"}
			str := []string{"high_risk", "fraud"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeFalse)
		})

		Convey("当检查空数组时", func() {
			arr := []string{"high_risk", "fraud"}
			str := []string{}

			result := ArrayContains(arr, str)
			So(result, ShouldBeTrue) // 空数组应该返回true
		})

		Convey("当源数组为空时", func() {
			arr := []string{}
			str := []string{"high_risk"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeFalse)
		})

		Convey("当两个数组都为空时", func() {
			arr := []string{}
			str := []string{}

			result := ArrayContains(arr, str)
			So(result, ShouldBeTrue)
		})

		Convey("测试单个元素", func() {
			arr := []string{"high_risk", "fraud", "bot"}
			str := []string{"fraud"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeTrue)
		})

		Convey("测试重复元素", func() {
			arr := []string{"high_risk", "fraud", "high_risk"}
			str := []string{"high_risk", "high_risk"}

			result := ArrayContains(arr, str)
			So(result, ShouldBeTrue)
		})
	})
}

func TestRiskTagWithDifferentTags(t *testing.T) {
	Convey("TestRiskTagWithDifferentTags", t, func() {
		Convey("测试 DeviceRiskTagForRole", func() {
			riskTag := &RiskTag{
				&DeviceRiskTagForRole{
					RiskTag: &model.RiskTag{
						Id:       403,
						ClientId: 1004,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{
									"role_risk",
									"account_sharing",
								},
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      900,
								ResultScore: 80,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1004,
				ModelCode: "test_model",
				RoleId:    "test_role_004",
				Doid:      "test_device_004",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := riskTag.ScoreKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)

			err := riskTag.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 测试逻辑流程是否正常
		})
	})
}

func TestRiskTagErrorCases(t *testing.T) {
	Convey("TestRiskTagErrorCases", t, func() {
		Convey("测试无效的评分配置", func() {
			riskTag := &RiskTag{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       404,
						ClientId: 1004,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{"high_risk"},
							},
						},
						ScoreRange: []model.ScoreRange{}, // 空的评分范围
					},
				},
			}

			param := &BaseParam{
				ClientId:  1004,
				ModelCode: "test_model",
				Nid:       "test_user_004",
				Doid:      "test_device_004",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := riskTag.CalcScore(ctx, param, &scoreItems)
			// 根据实际实现，这里可能返回错误或者正常处理
			// 需要根据 GetBoolScoreAndTLL 的实际行为来判断
			So(err, ShouldNotBeNil)
		})

		Convey("测试 nil TagCaliber", func() {
			riskTag := &RiskTag{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:         405,
						ClientId:   1004,
						ModelId:    "test_model",
						TagCaliber: nil, // nil TagCaliber
						ScoreRange: []model.ScoreRange{
							{
								Period:      900,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1004,
				ModelCode: "test_model",
				Nid:       "test_user_004",
				Doid:      "test_device_004",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := riskTag.CalcScore(ctx, param, &scoreItems)
			// 应该返回错误或者正常处理
			So(err, ShouldNotBeNil)
		})
	})
}

func TestRiskTagIntegration(t *testing.T) {
	Convey("TestRiskTagIntegration", t, func() {
		// 这是一个集成测试，测试完整的风险标签匹配流程
		Convey("完整的风险标签匹配流程", func() {
			riskTag := &RiskTag{
				&DeviceRiskTag{
					RiskTag: &model.RiskTag{
						Id:       406,
						ClientId: 1004,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Threshold: model.Threshold{
								DeviceRiskLabels: []string{
									"test_label1,test_label2",
									"single_label",
								},
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      1200,
								ResultScore: 90,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1004,
				ModelCode: "test_model",
				Nid:       "test_user_004",
				Doid:      "test_device_004",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := riskTag.ScoreKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)

			err := riskTag.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 测试标签解析逻辑
			threshold, err := riskTag.GetTag().GetThresholdScore()
			So(err, ShouldBeNil)
			So(len(threshold.DeviceRiskLabels), ShouldEqual, 2)

			// 测试标签分割逻辑
			var allLabels [][]string
			for _, label := range threshold.DeviceRiskLabels {
				p := strings.Split(label, ",")
				allLabels = append(allLabels, p)
			}
			So(len(allLabels), ShouldEqual, 2)
			So(len(allLabels[0]), ShouldEqual, 2) // "test_label1,test_label2"
			So(len(allLabels[1]), ShouldEqual, 1) // "single_label"
		})
	})
}
