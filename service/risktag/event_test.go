package risktag

import (
	"os"
	"path"
	"runtime"
	"score/config"
	"score/database"
	"testing"

	"gitlab.papegames.com/fringe/sparrow/pkg/testing/compose"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
)

func TestMain(m *testing.M) {
	_, filename, _, _ := runtime.Caller(0)
	cfg := path.Join(path.Dir(filename), "docker-compose.yaml")
	compose.Setup(cfg)
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	exitCode := m.Run()
	compose.Down()
	os.Exit(exitCode)
}
