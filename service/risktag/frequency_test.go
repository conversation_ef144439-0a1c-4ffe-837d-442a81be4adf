package risktag

import (
	"context"
	"score/database"
	"score/model"
	"strings"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestFrequencyCalcScore(t *testing.T) {
	Convey("TestFrequencyCalcScore", t, func() {
		// 创建测试用的 Frequency 实例
		frequency := &Frequency{
			&DeviceFrequency{
				RiskTag: &model.RiskTag{
					Id:        200,
					ClientId:  1002,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestFrequency",
					Code:      "test_frequency",
					Status:    1,
					TagUnit:   1, // bool
					RiskType:  1, // 增分
					ScoreType: 1, // 静态
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 60, // 60秒窗口
						},
						Threshold: model.Threshold{
							LoginTimes: 3, // 阈值为3次
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      300, // 5分钟
							ResultScore: 60,  // 60分
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1002,
			ModelCode: "test_model",
			Nid:       "test_user_002",
			Doid:      "test_device_002",
			Now:       time.Now(),
		}

		ctx := context.Background()

		// 清理测试数据
		scoreKey := frequency.ScoreKey(ctx, param)
		recordKey := frequency.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		var scoreItems = make([]*ScoreItem, 0)

		Convey("When frequency is below threshold", func() {
			// 第一次和第二次调用，频率未达到阈值
			for i := 0; i < 2; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该没有评分结果
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("When frequency reaches threshold", func() {
			// 连续调用3次，达到阈值
			for i := 0; i < 3; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 第3次应该触发评分
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 60)
			So(scoreItems[len(scoreItems)-1].Value, ShouldEqual, "1")

			// 验证评分结果已存储
			result, err := database.GetRdb().Get(ctx, scoreKey).Result()
			So(err, ShouldBeNil)
			So(strings.HasPrefix(result, "60"), ShouldBeTrue)
		})

		Convey("When threshold is 0", func() {
			// 修改阈值为0
			frequency.GetTag().TagCaliber.Threshold.LoginTimes = 0

			err := frequency.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该直接返回，不进行计算
			So(len(scoreItems), ShouldEqual, 0)
		})

		Convey("When Threshold is nil", func() {
			// 设置Threshold为nil
			originalThreshold := frequency.GetTag().TagCaliber.Threshold
			frequency.GetTag().TagCaliber = &model.TagCaliber{
				Param: model.Param{
					IntervalPeriod: 60,
				},
				Threshold: model.Threshold{}, // 空的threshold，LoginTimes默认为0
			}

			err := frequency.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 恢复原始设置
			frequency.GetTag().TagCaliber.Threshold = originalThreshold
		})
	})
}

func TestFrequencyEventRecord(t *testing.T) {
	Convey("TestFrequencyEventRecord", t, func() {
		frequency := &Frequency{
			&DeviceFrequency{
				RiskTag: &model.RiskTag{
					Id:       201,
					ClientId: 1002,
					ModelId:  "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1002,
			ModelCode: "test_model",
			Nid:       "test_user_002",
			Doid:      "test_device_002",
			Now:       time.Now(),
		}

		ctx := context.Background()

		// EventRecord 对于 Frequency 类型应该什么都不做
		err := frequency.EventRecord(ctx, param)
		So(err, ShouldBeNil)
	})
}

func TestFrequencyUseRedis(t *testing.T) {
	Convey("TestFrequencyUseRedis", t, func() {
		frequency := &Frequency{
			&DeviceFrequency{
				RiskTag: &model.RiskTag{
					Id:       202,
					ClientId: 1002,
					ModelId:  "test_model",
				},
			},
		}

		param := &BaseParam{
			ClientId:  1002,
			ModelCode: "test_model",
			Nid:       "test_user_002",
			Doid:      "test_device_002",
			Now:       time.Now(),
		}

		ctx := context.Background()
		window := time.Minute
		threshold := uint64(2)

		// 清理测试数据
		recordKey := frequency.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, recordKey)

		Convey("First call should return false", func() {
			result, err := frequency.UseRedis(ctx, param, window, threshold)
			So(err, ShouldBeNil)
			So(result, ShouldBeFalse)
		})

		Convey("Should return true when threshold is reached", func() {
			// 连续调用直到达到阈值
			var result bool
			var err error
			for i := uint64(1); i <= threshold; i++ {
				result, err = frequency.UseRedis(ctx, param, window, threshold)
				So(err, ShouldBeNil)
			}
			So(result, ShouldBeTrue)
		})

		Convey("Recalculate after window time expires", func() {
			// 先达到阈值
			for i := uint64(1); i <= threshold; i++ {
				frequency.UseRedis(ctx, param, window, threshold)
			}

			// 等待窗口过期（在实际测试中可能需要模拟时间）
			// 这里我们清理数据来模拟窗口过期
			database.GetRdb().Del(ctx, recordKey)

			// 重新开始计算
			result, err := frequency.UseRedis(ctx, param, window, threshold)
			So(err, ShouldBeNil)
			So(result, ShouldBeFalse) // 应该重新开始计算
		})
	})
}

func TestFrequencyWithDifferentTags(t *testing.T) {
	Convey("TestFrequencyWithDifferentTags", t, func() {
		Convey("Test IpFrequency", func() {
			frequency := &Frequency{
				&IpFrequency{
					RiskTag: &model.RiskTag{
						Id:       203,
						ClientId: 1002,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 60,
							},
							Threshold: model.Threshold{
								LoginTimes: 2,
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 40,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1002,
				ModelCode: "test_model",
				Nid:       "test_user_002",
				Ip:        "*************",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := frequency.ScoreKey(ctx, param)
			recordKey := frequency.RecordKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 连续调用达到阈值
			for i := 0; i < 2; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("Test RoleFrequency", func() {
			frequency := &Frequency{
				&RoleFrequency{
					RiskTag: &model.RiskTag{
						Id:       204,
						ClientId: 1002,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 60,
							},
							Threshold: model.Threshold{
								LoginTimes: 2,
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1002,
				ModelCode: "test_model",
				RoleId:    "test_role_002",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 清理测试数据
			scoreKey := frequency.ScoreKey(ctx, param)
			recordKey := frequency.RecordKey(ctx, param)
			database.GetRdb().Del(ctx, scoreKey)
			database.GetRdb().Del(ctx, recordKey)

			// 连续调用达到阈值
			for i := 0; i < 2; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})
	})
}

func TestFrequencyErrorCases(t *testing.T) {
	Convey("TestFrequencyErrorCases", t, func() {
		Convey("test invalid window time", func() {
			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       205,
						ClientId: 1002,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 0, // 无效的窗口时间
							},
							Threshold: model.Threshold{
								LoginTimes: 2,
							},
						},
						ScoreRange: []model.ScoreRange{
							{
								Period:      300,
								ResultScore: 50,
							},
						},
					},
				},
			}

			param := &BaseParam{
				ClientId:  1002,
				ModelCode: "test_model",
				Nid:       "test_user_002",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			err := frequency.CalcScore(ctx, param, &scoreItems)
			// 应该返回错误或者正常处理
			So(err, ShouldNotBeNil)
		})

		Convey("test invalid score range", func() {
			frequency := &Frequency{
				&DeviceFrequency{
					RiskTag: &model.RiskTag{
						Id:       206,
						ClientId: 1002,
						ModelId:  "test_model",
						TagCaliber: &model.TagCaliber{
							Param: model.Param{
								IntervalPeriod: 60,
							},
							Threshold: model.Threshold{
								LoginTimes: 2,
							},
						},
						ScoreRange: []model.ScoreRange{}, // 空的评分范围
					},
				},
			}

			param := &BaseParam{
				ClientId:  1002,
				ModelCode: "test_model",
				Nid:       "test_user_002",
				Now:       time.Now(),
			}

			ctx := context.Background()
			var scoreItems = make([]*ScoreItem, 0)

			// 连续调用达到阈值
			for i := 0; i < 2; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldNotBeNil) // 应该返回错误
			}
		})
	})
}
