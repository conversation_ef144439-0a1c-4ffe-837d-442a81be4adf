package risktag

import (
	"context"
	"score/database"
	"score/model"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestDeviceRoleCollect(t *testing.T) {
	Convey("TestDeviceRoleCollect", t, func() {
		collect := &Collect{
			&DeviceRoleCollect{
				RiskTag: &model.RiskTag{
					Id:        500,
					ClientId:  1005,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestDeviceRoleCollect",
					Code:      "test_device_role_collect",
					Status:    1,
					TagUnit:   1,
					RiskType:  1,
					ScoreType: 1,
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 3600,
						},
						Threshold: model.Threshold{
							DeviceLoginOpenIDNumbers: 3,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,
							ResultScore: 75,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Doid:      "test_device_005",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := collect.ScoreKey(ctx, param)
		recordKey := collect.RecordKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("Test device role collect calculation", func() {
			// 模拟多个角色使用同一设备
			for i := 1; i <= 4; i++ {
				param.RoleId = "test_role_" + string(rune('0'+i))
				err := collect.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			if len(scoreItems) > 0 {
				So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 75)
			}
		})

		Convey("Test key generation", func() {
			deviceRoleCollect := collect.CollectTag.(*DeviceRoleCollect)

			// 测试 RecordKey
			recordKey := deviceRoleCollect.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:cld:")
			So(recordKey, ShouldContainSubstring, "DoId")
			So(recordKey, ShouldContainSubstring, param.Doid)

			// 测试 ScoreKey
			scoreKey := deviceRoleCollect.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:cl:")
			So(scoreKey, ShouldContainSubstring, "RoleId")
			So(scoreKey, ShouldContainSubstring, param.RoleId)

			// 测试 GetMember
			member := deviceRoleCollect.GetMember(ctx, param)
			So(member, ShouldEqual, param.RoleId)
		})
	})
}

func TestDeviceRoleFrequency(t *testing.T) {
	Convey("TestDeviceRoleFrequency", t, func() {
		frequency := &Frequency{
			&DeviceRoleFrequency{
				RiskTag: &model.RiskTag{
					Id:        501,
					ClientId:  1005,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestDeviceRoleFrequency",
					Code:      "test_device_role_frequency",
					Status:    1,
					TagUnit:   1,
					RiskType:  1,
					ScoreType: 1,
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 60,
						},
						Threshold: model.Threshold{
							LoginTimes: 3,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      300,
							ResultScore: 65,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Doid:      "test_device_005",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := frequency.ScoreKey(ctx, param)
		recordKey := frequency.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试设备角色频率计算", func() {
			// 连续调用达到阈值
			for i := 0; i < 3; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			if len(scoreItems) > 0 {
				So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 65)
			}
		})

		Convey("测试键值生成", func() {
			deviceRoleFreq := frequency.BaseTag.(*DeviceRoleFrequency)

			// 测试 ScoreKey
			scoreKey := deviceRoleFreq.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:fqs:")
			So(scoreKey, ShouldContainSubstring, "RoleId")
			So(scoreKey, ShouldContainSubstring, param.RoleId)

			// 测试 RecordKey
			recordKey := deviceRoleFreq.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:fq:")
			So(recordKey, ShouldContainSubstring, "DoId")
			So(recordKey, ShouldContainSubstring, param.Doid)

			// 测试 NeedRecord
			needRecord := deviceRoleFreq.NeedRecord(ctx, param)
			So(needRecord, ShouldBeFalse) // DeviceRoleFrequency 不需要记录
		})
	})
}

func TestDeviceRoleIntervalTag(t *testing.T) {
	Convey("TestDeviceRoleInterval", t, func() {
		interval := &Interval{
			&DeviceRoleInterval{
				RiskTag: &model.RiskTag{
					Id:        502,
					ClientId:  1005,
					AppId:     "test_app",
					ModelId:   "test_model",
					Name:      "TestDeviceRoleInterval",
					Code:      "test_device_role_interval",
					Status:    1,
					TagUnit:   1,
					RiskType:  1,
					ScoreType: 1,
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							RequestIntervalPeriod: 5000, // 5秒
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      600,
							ResultScore: 85,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Doid:      "test_device_005",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := interval.ScoreKey(ctx, param)
		recordKey := interval.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试设备角色间隔计算", func() {
			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(2 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
			if len(scoreItems) > 0 {
				So(scoreItems[len(scoreItems)-1].Score, ShouldEqual, 85)
			}
		})

		Convey("测试键值生成", func() {
			deviceRoleInterval := interval.IntervalTag.(*DeviceRoleInterval)

			// 测试 ScoreKey
			scoreKey := deviceRoleInterval.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:sintval:")
			So(scoreKey, ShouldContainSubstring, "RoleId")
			So(scoreKey, ShouldContainSubstring, param.RoleId)

			// 测试 RecordKey
			recordKey := deviceRoleInterval.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:intval:")
			So(recordKey, ShouldContainSubstring, "DoId")
			So(recordKey, ShouldContainSubstring, param.Doid)

			// 测试 NeedRecord
			needRecord := deviceRoleInterval.NeedRecord(ctx, param)
			So(needRecord, ShouldBeTrue) // 有 RoleId 时需要记录

			// 测试没有 RoleId 的情况
			paramWithoutRole := &BaseParam{
				ClientId:  1005,
				ModelCode: "test_model",
				RoleId:    "",
				Doid:      "test_device_005",
				Now:       time.Now(),
			}
			needRecord = deviceRoleInterval.NeedRecord(ctx, paramWithoutRole)
			So(needRecord, ShouldBeFalse)
		})
	})
}

func TestIpRoleCollect(t *testing.T) {
	Convey("TestIpRoleCollect", t, func() {
		collect := &Collect{
			&IpRoleCollect{
				RiskTag: &model.RiskTag{
					Id:       503,
					ClientId: 1005,
					ModelId:  "test_model",
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 3600,
						},
						Threshold: model.Threshold{
							DeviceLoginOpenIDNumbers: 2,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      300,
							ResultScore: 55,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Ip:        "192.168.1.500",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := collect.ScoreKey(ctx, param)
		recordKey := collect.RecordKey(ctx, param)
		database.GetCaclRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试IP角色聚集计算", func() {
			// 模拟多个角色使用同一IP
			for i := 1; i <= 3; i++ {
				param.RoleId = "test_role_" + string(rune('0'+i))
				err := collect.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试键值生成", func() {
			ipRoleCollect := collect.CollectTag.(*IpRoleCollect)

			// 测试 RecordKey
			recordKey := ipRoleCollect.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:cld:")
			So(recordKey, ShouldContainSubstring, "Ip")
			So(recordKey, ShouldContainSubstring, param.Ip)

			// 测试 ScoreKey
			scoreKey := ipRoleCollect.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:cl:")
			So(scoreKey, ShouldContainSubstring, "RoleId")
			So(scoreKey, ShouldContainSubstring, param.RoleId)

			// 测试 GetMember
			member := ipRoleCollect.GetMember(ctx, param)
			So(member, ShouldEqual, param.RoleId)
		})
	})
}

func TestIpRoleFrequency(t *testing.T) {
	Convey("TestIpRoleFrequency", t, func() {
		frequency := &Frequency{
			&IpRoleFrequency{
				RiskTag: &model.RiskTag{
					Id:       504,
					ClientId: 1005,
					ModelId:  "test_model",
					TagCaliber: &model.TagCaliber{
						Param: model.Param{
							IntervalPeriod: 60,
						},
						Threshold: model.Threshold{
							LoginTimes: 2,
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      300,
							ResultScore: 45,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Ip:        "192.168.1.500",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := frequency.ScoreKey(ctx, param)
		recordKey := frequency.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试IP角色频率计算", func() {
			// 连续调用达到阈值
			for i := 0; i < 2; i++ {
				err := frequency.CalcScore(ctx, param, &scoreItems)
				So(err, ShouldBeNil)
			}

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试键值生成", func() {
			ipRoleFreq := frequency.BaseTag.(*IpRoleFrequency)

			// 测试 ScoreKey
			scoreKey := ipRoleFreq.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:fqs:")
			So(scoreKey, ShouldContainSubstring, "RoleId")

			// 测试 RecordKey
			recordKey := ipRoleFreq.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:fq:")
			So(recordKey, ShouldContainSubstring, "Ip")

			// 测试 NeedRecord
			needRecord := ipRoleFreq.NeedRecord(ctx, param)
			So(needRecord, ShouldBeFalse)
		})
	})
}

func TestIpRoleIntervalTag(t *testing.T) {
	Convey("TestIpRoleInterval", t, func() {
		interval := &Interval{
			&IpRoleInterval{
				RiskTag: &model.RiskTag{
					Id:       505,
					ClientId: 1005,
					ModelId:  "test_model",
					TagCaliber: &model.TagCaliber{
						Threshold: model.Threshold{
							RequestIntervalPeriod: 3000, // 3秒
						},
					},
					ScoreRange: []model.ScoreRange{
						{
							Period:      300,
							ResultScore: 35,
						},
					},
				},
			},
		}

		param := &BaseParam{
			ClientId:  1005,
			ModelCode: "test_model",
			RoleId:    "test_role_005",
			Ip:        "192.168.1.500",
			Now:       time.Now(),
		}

		ctx := context.Background()
		var scoreItems = make([]*ScoreItem, 0)

		// 清理测试数据
		scoreKey := interval.ScoreKey(ctx, param)
		recordKey := interval.RecordKey(ctx, param)
		database.GetRdb().Del(ctx, scoreKey)
		database.GetRdb().Del(ctx, recordKey)

		Convey("测试IP角色间隔计算", func() {
			// 先记录时间
			err := interval.EventRecord(ctx, param)
			So(err, ShouldBeNil)

			// 短时间间隔
			param.Now = param.Now.Add(1 * time.Second)
			err = interval.CalcScore(ctx, param, &scoreItems)
			So(err, ShouldBeNil)

			// 应该有评分结果
			So(len(scoreItems), ShouldBeGreaterThan, 0)
		})

		Convey("测试键值生成和条件判断", func() {
			ipRoleInterval := interval.IntervalTag.(*IpRoleInterval)

			// 测试 ScoreKey
			scoreKey := ipRoleInterval.ScoreKey(ctx, param)
			So(scoreKey, ShouldContainSubstring, "risk:sintval:")
			So(scoreKey, ShouldContainSubstring, "RoleId")

			// 测试 RecordKey
			recordKey := ipRoleInterval.RecordKey(ctx, param)
			So(recordKey, ShouldContainSubstring, "risk:fq:")
			So(recordKey, ShouldContainSubstring, "Ip")

			// 测试 NeedRecord
			needRecord := ipRoleInterval.NeedRecord(ctx, param)
			So(needRecord, ShouldBeTrue) // 有 IP 时需要记录

			// 测试没有 IP 的情况
			paramWithoutIp := &BaseParam{
				ClientId:  1005,
				ModelCode: "test_model",
				RoleId:    "test_role_005",
				Ip:        "",
				Now:       time.Now(),
			}
			needRecord = ipRoleInterval.NeedRecord(ctx, paramWithoutIp)
			So(needRecord, ShouldBeFalse)
		})
	})
}
