# service
host: "127.0.0.1:9980"
register: false

sparrow:
  log:
    file: "stdout"
    encoding: "console"
    rotate: "hourly"
    buffer: 4096
    level: "debug"

  database:
    mysql:
      dataSource: "root:BozsCMdcrj@(************:3306)/pay_risk?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5
    redis:
      addr: 127.0.0.1:6379
      password:
      pool_size: 5
      max_retries: 2
  broker:
    kafka:
      writer:
        Brokers:
          - *************:9092
        Topic: t-risk-score